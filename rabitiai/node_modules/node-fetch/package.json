{"name": "node-fetch", "version": "3.3.2", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "files": ["src", "@types/index.d.ts"], "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}}