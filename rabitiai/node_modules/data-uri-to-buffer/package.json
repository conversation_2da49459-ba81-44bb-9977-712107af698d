{"name": "data-uri-to-buffer", "version": "4.0.1", "description": "Generate a Buffer instance from a Data URI string", "type": "module", "exports": "./dist/index.js", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "tsc", "test": "jest", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "engines": {"node": ">= 12"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^12.20.36", "jest": "^27.3.1", "ts-jest": "^27.0.7", "typescript": "^4.4.4"}, "jest": {"preset": "ts-jest", "globals": {"ts-jest": {"diagnostics": false, "isolatedModules": true}}, "verbose": false, "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.test.ts"]}}