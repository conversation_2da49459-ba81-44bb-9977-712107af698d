// RabitiAI Airdrop Mission Bot - Node.js (Enhanced Version)
// Usage: node bot.js

const axios = require("axios");

const WALLET = "******************************************"; // Ganti dengan wallet kamu
const BASE_URL = "https://beta.rabitiai.net";

const headers = {
  Accept: "application/json",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  Origin: BASE_URL,
  Referer: BASE_URL,
};

// Konfigurasi bot
const CONFIG = {
  DELAY_BETWEEN_MISSIONS: 2000, // 2 detik delay antar misi
  RETRY_ATTEMPTS: 3,
  SKIP_MANUAL_MISSIONS: true, // Skip misi yang butuh aksi manual (Twitter, Telegram, dll)
  PRIORITY_MISSIONS: ['daily_checkin', 'claim_faucet'], // Misi prioritas tinggi
};

// Daftar misi yang butuh aksi manual
const MANUAL_MISSIONS = [
  'like_retweet_pinned',
  'quote_with_hashtag',
  'feedback_tweet',
  'follow_on_x',
  'join_telegram',
  'subscribe_channel',
  'tag_friends_3',
  'share_post',
  'follow_nortiyus',
  'follow_d5wav',
  'make_it_go_viral',
  'invite_friends_1',
  'invite_friends_5',
  'invite_friends_10'
];

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function formatReward(reward) {
  return reward >= 1000 ? `${(reward/1000).toFixed(1)}k` : reward.toString();
}

function getMissionIcon(mission) {
  const icons = {
    'daily_checkin': '📅',
    'claim_faucet': '💧',
    'like_retweet_pinned': '❤️',
    'quote_with_hashtag': '💬',
    'feedback_tweet': '📝',
    'follow_on_x': '🐦',
    'join_telegram': '📱',
    'subscribe_channel': '📺',
    'tag_friends_3': '👥',
    'share_post': '🔄',
    'follow_nortiyus': '👤',
    'follow_d5wav': '👤',
    'make_it_go_viral': '🚀',
    'invite_friends_1': '🎯',
    'invite_friends_5': '🎯',
    'invite_friends_10': '🎯'
  };
  return icons[mission.code] || '⭐';
}

async function getMissions() {
  try {
    console.log("🔍 Mengambil daftar misi...");
    const res = await axios.get(`${BASE_URL}/api/rabiti-ai/mission`, { headers });

    if (!res.data || !Array.isArray(res.data)) {
      console.log("❌ Format data misi tidak valid");
      return [];
    }

    const activeMissions = res.data.filter(m => m.status === "active");
    console.log(`📋 Ditemukan ${activeMissions.length} misi aktif`);

    // Kelompokkan misi berdasarkan tipe
    const dailyMissions = activeMissions.filter(m => m.type === "daily");
    const onceMissions = activeMissions.filter(m => m.type === "once");

    console.log(`   📅 Misi harian: ${dailyMissions.length}`);
    console.log(`   🎯 Misi sekali: ${onceMissions.length}`);

    return activeMissions;
  } catch (err) {
    console.error("❌ Gagal mengambil daftar misi:", err.message);
    return [];
  }
}

async function doMission(mission, attempt = 1) {
  const icon = getMissionIcon(mission);
  const reward = formatReward(mission.reward);

  try {
    const res = await axios.put(
      `${BASE_URL}/api/airdrop/do-mission/${WALLET}/${mission.code}`,
      {},
      { headers }
    );

    console.log(`✅ ${icon} [${mission.code}] ${mission.title} (+${reward} XP)`);
    if (res.data.message) {
      console.log(`   💬 ${res.data.message}`);
    }
    return true;

  } catch (err) {
    const msg = err.response?.data?.message || err.message;

    if (attempt < CONFIG.RETRY_ATTEMPTS && !msg.includes("already completed")) {
      console.log(`⚠️  ${icon} [${mission.code}] Gagal (percobaan ${attempt}/${CONFIG.RETRY_ATTEMPTS}): ${msg}`);
      await sleep(CONFIG.DELAY_BETWEEN_MISSIONS);
      return doMission(mission, attempt + 1);
    }

    if (msg.includes("already completed") || msg.includes("sudah selesai")) {
      console.log(`✅ ${icon} [${mission.code}] Sudah selesai sebelumnya (+${reward} XP)`);
      return true;
    }

    console.log(`❌ ${icon} [${mission.code}] ${mission.title}: ${msg}`);
    return false;
  }
}

async function checkIn() {
  try {
    console.log("📅 Melakukan check-in harian...");
    const res = await axios.post(`${BASE_URL}/api/airdrop/checkin/${WALLET}`, {}, { headers });
    console.log(`✅ Check-in berhasil: ${res.data.message}`);
    return true;
  } catch (err) {
    const msg = err.response?.data?.message || err.message;
    if (msg.includes("already checked in") || msg.includes("sudah check-in")) {
      console.log("✅ Sudah check-in hari ini");
      return true;
    }
    console.log(`❌ Check-in gagal: ${msg}`);
    return false;
  }
}

async function getInfo() {
  try {
    console.log("📊 Mengambil info wallet...");
    const res = await axios.get(`${BASE_URL}/api/airdrop/info/${WALLET}?refCode=`, { headers });

    if (res.data) {
      console.log("📊 Info Airdrop:");
      console.log(`   💰 Total XP: ${res.data.totalXp || 0}`);
      console.log(`   🎯 Misi selesai: ${res.data.completedMissions || 0}`);
      console.log(`   👥 Referral: ${res.data.referralCount || 0}`);
      if (res.data.rank) console.log(`   🏆 Rank: ${res.data.rank}`);
    }
    return true;
  } catch (err) {
    console.log("❌ Gagal mengambil info wallet:", err.message);
    return false;
  }
}

async function processMissions(missions) {
  if (missions.length === 0) {
    console.log("❌ Tidak ada misi yang tersedia");
    return;
  }

  // Urutkan misi berdasarkan prioritas
  const sortedMissions = missions.sort((a, b) => {
    // Prioritas 1: Misi prioritas tinggi
    const aPriority = CONFIG.PRIORITY_MISSIONS.includes(a.code);
    const bPriority = CONFIG.PRIORITY_MISSIONS.includes(b.code);
    if (aPriority && !bPriority) return -1;
    if (!aPriority && bPriority) return 1;

    // Prioritas 2: Misi otomatis vs manual
    const aManual = MANUAL_MISSIONS.includes(a.code);
    const bManual = MANUAL_MISSIONS.includes(b.code);
    if (!aManual && bManual) return -1;
    if (aManual && !bManual) return 1;

    // Prioritas 3: Reward tertinggi
    return b.reward - a.reward;
  });

  let completed = 0;
  let skipped = 0;
  let failed = 0;

  console.log(`\n🎮 Memulai eksekusi ${missions.length} misi...\n`);

  for (const mission of sortedMissions) {
    // Skip misi manual jika diatur
    if (CONFIG.SKIP_MANUAL_MISSIONS && MANUAL_MISSIONS.includes(mission.code)) {
      const icon = getMissionIcon(mission);
      const reward = formatReward(mission.reward);
      console.log(`⏭️  ${icon} [${mission.code}] Dilewati (butuh aksi manual) (+${reward} XP)`);
      console.log(`   🔗 Link: ${mission.link || 'Tidak ada link'}`);
      skipped++;
      continue;
    }

    const success = await doMission(mission);
    if (success) {
      completed++;
    } else {
      failed++;
    }

    // Delay antar misi
    if (CONFIG.DELAY_BETWEEN_MISSIONS > 0) {
      await sleep(CONFIG.DELAY_BETWEEN_MISSIONS);
    }
  }

  console.log(`\n📈 Ringkasan eksekusi misi:`);
  console.log(`   ✅ Berhasil: ${completed}`);
  console.log(`   ⏭️  Dilewati: ${skipped}`);
  console.log(`   ❌ Gagal: ${failed}`);
  console.log(`   📊 Total: ${missions.length}`);
}

(async () => {
  console.log("🚀 RabitiAI Airdrop Bot - Enhanced Version");
  console.log(`💼 Wallet: ${WALLET}`);
  console.log(`⚙️  Konfigurasi:`);
  console.log(`   ⏱️  Delay antar misi: ${CONFIG.DELAY_BETWEEN_MISSIONS}ms`);
  console.log(`   🔄 Retry attempts: ${CONFIG.RETRY_ATTEMPTS}`);
  console.log(`   ⏭️  Skip manual missions: ${CONFIG.SKIP_MANUAL_MISSIONS}`);
  console.log("=" .repeat(50));

  // 1. Check-in harian
  await checkIn();
  await sleep(1000);

  // 2. Ambil info wallet
  await getInfo();
  await sleep(1000);

  // 3. Ambil dan proses misi
  const missions = await getMissions();
  await processMissions(missions);

  console.log("\n🎉 Bot selesai dijalankan!");
  console.log("💡 Tip: Untuk misi manual, kunjungi link yang ditampilkan secara manual");
})();
