const axios = require("axios");
const crypto = require("crypto");

const BASE_URL = "https://beta.rabitiai.net";
const REF_CODE = "SO1TN6";

let WALLET = "";
let COOKIE = "";

const headers = {
  Accept: "application/json",
  "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  Origin: BASE_URL,
};

const headersWithCookie = {
  Accept: "application/json, text/plain, */*",
  "Accept-Language": "id-ID,id;q=0.9",
  "Accept-Encoding": "gzip, deflate, br",
  "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138"',
  "Sec-Ch-Ua-Mobile": "?0",
  "Sec-Ch-Ua-Platform": '"Linux"',
  "Sec-Fetch-Site": "same-origin",
  "Sec-Fetch-Mode": "cors",
  "Sec-Fetch-Dest": "empty",
  Origin: BASE_URL,
  Referer: `${BASE_URL}/?ref=${REF_CODE}`,
  Priority: "u=1, i"
};

const CONFIG = {
  DELAY_BETWEEN_MISSIONS: 2000,
  RETRY_ATTEMPTS: 3,
  SKIP_MANUAL_MISSIONS: false,
  PRIORITY_MISSIONS: ['daily_checkin', 'claim_faucet'],
  USE_COOKIE_FOR_MISSIONS: true,
  AUTO_GENERATE_WALLET: true,
  RUN_ALL_MISSIONS: true,
};

const MISSIONS_NEED_COOKIE = [
  'follow_on_x',
  'join_telegram',
  'subscribe_channel',
  'like_retweet_pinned',
  'quote_with_hashtag',
  'feedback_tweet',
  'tag_friends_3',
  'share_post',
  'follow_nortiyus',
  'follow_d5wav',
  'make_it_go_viral'
];

const MANUAL_MISSIONS = [
  'invite_friends_1',
  'invite_friends_5',
  'invite_friends_10'
];

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function generateRandomWallet() {
  const chars = '0123456789abcdef';
  let wallet = '0x';
  for (let i = 0; i < 40; i++) {
    wallet += chars[Math.floor(Math.random() * chars.length)];
  }
  return wallet;
}

function generateRandomCookie() {
  const timestamp = Date.now();
  const random1 = Math.floor(Math.random() * 1000000000000000);
  const random2 = Math.floor(Math.random() * 1000000000000000);

  return `_fbp=fb.1.${timestamp}.${random1}; _ga=GA1.1.${random2}.${timestamp}; __adroll_fpc=${crypto.randomBytes(16).toString('hex')}-${timestamp}; referrer=https://beta.rabitiai.net/; refAddress=${REF_CODE}; __ar_v4=%7C4NJN6I7CJ5DODAYVQHOTNF%3A20250802%3A1%7C3VNZRTAYRZAHVG32BUJOHR%3A20250802%3A2; _ga_NFNB12TCBM=GS2.1.s${timestamp}$o1$g1$t${timestamp + 1000}$j60$l0$h0`;
}

async function registerWallet() {
  try {
    console.log("🔄 Membuat wallet baru...");

    if (CONFIG.AUTO_GENERATE_WALLET) {
      WALLET = generateRandomWallet();
      COOKIE = generateRandomCookie();
      console.log(`✅ Wallet dibuat: ${WALLET}`);
      console.log(`✅ Cookie dibuat untuk session`);
    }

    console.log("🔗 Mendaftarkan wallet dengan referral code...");
    const requestHeaders = {
      ...headersWithCookie,
      Cookie: COOKIE
    };

    const registerRes = await axios.post(
      `${BASE_URL}/api/airdrop/register`,
      {
        wallet: WALLET,
        refCode: REF_CODE
      },
      { headers: requestHeaders }
    );

    console.log(`✅ Wallet terdaftar dengan ref: ${REF_CODE}`);
    if (registerRes.data.message) {
      console.log(`   💬 ${registerRes.data.message}`);
    }

    return true;
  } catch (err) {
    const msg = err.response?.data?.message || err.message;
    if (msg.includes("already registered") || msg.includes("sudah terdaftar")) {
      console.log("✅ Wallet sudah terdaftar sebelumnya");
      return true;
    }
    console.log(`❌ Gagal mendaftarkan wallet: ${msg}`);
    return false;
  }
}

function formatReward(reward) {
  return reward >= 1000 ? `${(reward/1000).toFixed(1)}k` : reward.toString();
}

function getMissionIcon(mission) {
  const icons = {
    'daily_checkin': '📅',
    'claim_faucet': '💧',
    'like_retweet_pinned': '❤️',
    'quote_with_hashtag': '💬',
    'feedback_tweet': '📝',
    'follow_on_x': '🐦',
    'join_telegram': '📱',
    'subscribe_channel': '📺',
    'tag_friends_3': '👥',
    'share_post': '🔄',
    'follow_nortiyus': '👤',
    'follow_d5wav': '👤',
    'make_it_go_viral': '🚀'
  };
  return icons[mission.code] || '⭐';
}

async function getMissions() {
  try {
    console.log("🔍 Mengambil daftar misi...");
    const res = await axios.get(`${BASE_URL}/api/rabiti-ai/mission`, { headers });

    if (!res.data || !Array.isArray(res.data)) {
      console.log("❌ Format data misi tidak valid");
      return [];
    }

    const activeMissions = res.data.filter(m => m.status === "active");
    console.log(`📋 Ditemukan ${activeMissions.length} misi aktif`);

    const dailyMissions = activeMissions.filter(m => m.type === "daily");
    const onceMissions = activeMissions.filter(m => m.type === "once");

    console.log(`   📅 Misi harian: ${dailyMissions.length}`);
    console.log(`   🎯 Misi sekali: ${onceMissions.length}`);

    return activeMissions;
  } catch (err) {
    console.error("❌ Gagal mengambil daftar misi:", err.message);
    return [];
  }
}

async function doMission(mission, attempt = 1) {
  const icon = getMissionIcon(mission);
  const reward = formatReward(mission.reward);

  const needsCookie = CONFIG.USE_COOKIE_FOR_MISSIONS && MISSIONS_NEED_COOKIE.includes(mission.code);
  const requestHeaders = needsCookie ? { ...headersWithCookie, Cookie: COOKIE } : headers;

  try {
    const res = await axios.put(
      `${BASE_URL}/api/airdrop/do-mission/${WALLET}/${mission.code}`,
      {},
      { headers: requestHeaders }
    );

    const totalPoints = res.data?.data?.totalPoint || 'N/A';
    console.log(`✅ ${icon} [${mission.code}] ${mission.title} (+${reward} XP) | Total: ${totalPoints}`);
    if (res.data.message) {
      console.log(`   💬 ${res.data.message}`);
    }
    return true;

  } catch (err) {
    const msg = err.response?.data?.message || err.message;

    if (attempt < CONFIG.RETRY_ATTEMPTS && !msg.includes("already completed") && !msg.includes("Mission already completed")) {
      console.log(`⚠️  ${icon} [${mission.code}] Gagal (percobaan ${attempt}/${CONFIG.RETRY_ATTEMPTS}): ${msg}`);
      await sleep(CONFIG.DELAY_BETWEEN_MISSIONS);
      return doMission(mission, attempt + 1);
    }

    if (msg.includes("already completed") || msg.includes("Mission already completed") || msg.includes("sudah selesai")) {
      console.log(`✅ ${icon} [${mission.code}] Sudah selesai sebelumnya (+${reward} XP)`);
      return true;
    }

    console.log(`❌ ${icon} [${mission.code}] ${mission.title}: ${msg}`);
    return false;
  }
}

async function checkIn() {
  try {
    console.log("📅 Melakukan check-in harian...");
    const requestHeaders = CONFIG.USE_COOKIE_FOR_MISSIONS ? { ...headersWithCookie, Cookie: COOKIE } : headers;
    const res = await axios.post(`${BASE_URL}/api/airdrop/checkin/${WALLET}`, {}, { headers: requestHeaders });
    const totalPoints = res.data?.data?.totalPoint || 'N/A';
    console.log(`✅ Check-in berhasil: ${res.data.message} | Total: ${totalPoints}`);
    return true;
  } catch (err) {
    const msg = err.response?.data?.message || err.message;
    if (msg.includes("already checked in") || msg.includes("sudah check-in")) {
      console.log("✅ Sudah check-in hari ini");
      return true;
    }
    console.log(`❌ Check-in gagal: ${msg}`);
    return false;
  }
}

async function getInfo() {
  try {
    console.log("📊 Mengambil info wallet...");
    const requestHeaders = CONFIG.USE_COOKIE_FOR_MISSIONS ? { ...headersWithCookie, Cookie: COOKIE } : headers;
    const res = await axios.get(`${BASE_URL}/api/airdrop/info/${WALLET}?refCode=${REF_CODE}`, { headers: requestHeaders });

    if (res.data) {
      console.log("📊 Info Airdrop:");
      console.log(`   💰 Total XP: ${res.data.totalXp || 0}`);
      console.log(`   🎯 Misi selesai: ${res.data.completedMissions || 0}`);
      console.log(`   👥 Referral: ${res.data.referralCount || 0}`);
      console.log(`   🔗 Ref Code: ${REF_CODE}`);
      if (res.data.rank) console.log(`   🏆 Rank: ${res.data.rank}`);
    }
    return true;
  } catch (err) {
    console.log("❌ Gagal mengambil info wallet:", err.message);
    return false;
  }
}

async function processMissions(missions) {
  if (missions.length === 0) {
    console.log("❌ Tidak ada misi yang tersedia");
    return;
  }

  const sortedMissions = missions.sort((a, b) => {
    const aPriority = CONFIG.PRIORITY_MISSIONS.includes(a.code);
    const bPriority = CONFIG.PRIORITY_MISSIONS.includes(b.code);
    if (aPriority && !bPriority) return -1;
    if (!aPriority && bPriority) return 1;

    const aManual = MANUAL_MISSIONS.includes(a.code);
    const bManual = MANUAL_MISSIONS.includes(b.code);
    if (!aManual && bManual) return -1;
    if (aManual && !bManual) return 1;

    return b.reward - a.reward;
  });

  let completed = 0;
  let skipped = 0;
  let failed = 0;

  console.log(`\n🎮 Memulai eksekusi ${missions.length} misi...\n`);

  for (const mission of sortedMissions) {
    if (CONFIG.SKIP_MANUAL_MISSIONS && MANUAL_MISSIONS.includes(mission.code)) {
      const icon = getMissionIcon(mission);
      const reward = formatReward(mission.reward);
      console.log(`⏭️  ${icon} [${mission.code}] Dilewati (butuh aksi manual) (+${reward} XP)`);
      console.log(`   🔗 Link: ${mission.link || 'Tidak ada link'}`);
      skipped++;
      continue;
    }

    const success = await doMission(mission);
    if (success) {
      completed++;
    } else {
      failed++;
    }

    if (CONFIG.DELAY_BETWEEN_MISSIONS > 0) {
      await sleep(CONFIG.DELAY_BETWEEN_MISSIONS);
    }
  }

  console.log(`\n📈 Ringkasan eksekusi misi:`);
  console.log(`   ✅ Berhasil: ${completed}`);
  console.log(`   ⏭️  Dilewati: ${skipped}`);
  console.log(`   ❌ Gagal: ${failed}`);
  console.log(`   📊 Total: ${missions.length}`);
}

(async () => {
  console.log("🚀 RabitiAI Airdrop Bot - Auto Wallet & Mission Runner");
  console.log(`� Ref Code: ${REF_CODE}`);
  console.log(`⚙️  Konfigurasi:`);
  console.log(`   🔄 Auto generate wallet: ${CONFIG.AUTO_GENERATE_WALLET}`);
  console.log(`   🎯 Run all missions: ${CONFIG.RUN_ALL_MISSIONS}`);
  console.log(`   ⏱️  Delay antar misi: ${CONFIG.DELAY_BETWEEN_MISSIONS}ms`);
  console.log(`   🔄 Retry attempts: ${CONFIG.RETRY_ATTEMPTS}`);
  console.log(`   ⏭️  Skip manual missions: ${CONFIG.SKIP_MANUAL_MISSIONS}`);
  console.log(`   🍪 Use cookie: ${CONFIG.USE_COOKIE_FOR_MISSIONS}`);
  console.log("=" .repeat(60));

  const success = await registerWallet();
  if (!success) {
    console.log("❌ Gagal mendaftarkan wallet, menghentikan bot");
    return;
  }

  console.log(`💼 Wallet aktif: ${WALLET}`);
  await sleep(2000);

  await checkIn();
  await sleep(1000);

  await getInfo();
  await sleep(1000);

  const missions = await getMissions();
  await processMissions(missions);

  console.log("\n🎉 Bot selesai dijalankan!");
  console.log(`� Wallet: ${WALLET}`);
  console.log(`🔗 Referral: ${REF_CODE}`);
  console.log("💡 Semua misi otomatis telah dijalankan!");
})();
