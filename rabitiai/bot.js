// RabitiAI Airdrop Mission Bot - Node.js
// Usage: node bot.js

const axios = require("axios");

const WALLET = "******************************************"; // Ganti dengan wallet kamu
const BASE_URL = "https://beta.rabitiai.net";

const headers = {
  Accept: "application/json",
  "User-Agent": "Mozilla/5.0",
  Origin: BASE_URL,
};

async function getMissions() {
  try {
    const res = await axios.get(`${BASE_URL}/api/rabiti-ai/mission`, { headers });
    return res.data.filter((m) => m.status === "active").map((m) => m.code);
  } catch (err) {
    console.error("Gagal mengambil daftar misi:", err.message);
    return [];
  }
}

async function doMission(code) {
  try {
    const res = await axios.put(
      `${BASE_URL}/api/airdrop/do-mission/${WALLET}/${code}`,
      {},
      { headers }
    );
    console.log(`✔️  [${code}] => ${res.data.message}`);
  } catch (err) {
    const msg = err.response?.data?.message || "Request error";
    console.log(`❌ [${code}] => ${msg}`);
  }
}

async function checkIn() {
  try {
    const res = await axios.post(`${BASE_URL}/api/airdrop/checkin/${WALLET}`, {}, { headers });
    console.log(`📅 Check-in: ${res.data.message}`);
  } catch (err) {
    const msg = err.response?.data?.message || err.message;
    console.log(`📅 Check-in Error: ${msg}`);
  }
}

async function getInfo() {
  try {
    const res = await axios.get(`${BASE_URL}/api/airdrop/info/${WALLET}?refCode=`, { headers });
    console.log("📊 Info Airdrop:", res.data);
  } catch (err) {
    console.log("Gagal mengambil info wallet");
  }
}

(async () => {
  console.log("🚀 Mulai bot RabitiAI untuk wallet:", WALLET);
  await checkIn();
  await getInfo();

  const missions = await getMissions();
  for (let code of missions) {
    await doMission(code);
  }
})();
